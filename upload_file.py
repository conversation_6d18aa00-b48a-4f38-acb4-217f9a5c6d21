import base64
import requests
import os

# Leer el archivo
file_path = "REPRESENTACIÓN ADVOS.docx"
with open(file_path, "rb") as file:
    file_content = file.read()

# Convertir a base64
base64_content = base64.b64encode(file_content).decode('utf-8')

# Configurar la subida a Supabase
project_ref = "twxtlrjbpxifcdzepxfk"
bucket_name = "DOCUMENTOS"
file_name = "REPRESENTACION/REPRESENTACIÓN ADVOS.docx"

# URL de la API de Supabase Storage
url = f"https://{project_ref}.supabase.co/storage/v1/object/{bucket_name}/{file_name}"

# Headers (necesitaremos el token de autorización)
headers = {
    "Content-Type": "application/octet-stream",
    "Authorization": "Bearer YOUR_SUPABASE_ANON_KEY"  # Esto se reemplazará
}

print(f"Base64 content length: {len(base64_content)}")
print(f"Upload URL: {url}")
print("File ready for upload")

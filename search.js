// search.js - Lógica para la página de búsqueda general

document.addEventListener('DOMContentLoaded', () => {
    const searchForm = document.getElementById('search-form');
    const searchTypeInput = document.getElementById('search-type');
    const searchIdInput = document.getElementById('search-id');
    const searchNameInput = document.getElementById('search-name');
    const clearSearchButton = document.getElementById('clear-search');
    const searchResultsContainer = document.getElementById('search-results');
    const searchLoader = document.getElementById('search-loader');
    const searchMessage = document.getElementById('search-message');

    const tableDisplayNames = { 'Exp_Advos': 'Administrativo', 'Nacionalidad': 'Nacionalidad', 'Proc_Judiciales': 'Judicial', 'Datos_Generales': 'General' };
    const searchableColumns = { 'Exp_Advos': { idFields: ['N.I.E1', 'PASAPORTE1'], nameFields: ['Nombre1', '1er Apellido1', '2º Apellido1'] }, 'Nacionalidad': { idFields: ['NIE', 'Nº Soporte'], nameFields: ['NOMBRE', 'PRIMER APELLIDO', 'SEGUNDO APELLIDO'] }, 'Proc_Judiciales': { idFields: ['NIE/PASAPORTE'], nameFields: ['NOMBRE'] }, 'Datos_Generales': { idFields: [], nameFields: ['NOMBRE', 'PROCEDIMIENTO', 'COMENTARIO'] } };

    async function handleSearch(event) {
        event.preventDefault();
        const idTerm = searchIdInput.value.trim();
        const nameTerm = searchNameInput.value.trim();
        const typeFilter = searchTypeInput.value;

        if (!idTerm && !nameTerm) {
            searchMessage.textContent = 'Por favor, introduce un término de búsqueda.';
            searchResultsContainer.innerHTML = ''; return;
        }
        searchLoader.classList.remove('hidden');
        searchResultsContainer.innerHTML = '';
        searchMessage.textContent = '';
        try {
            const tablesToSearch = typeFilter === 'Todos' ? Object.keys(searchableColumns) : [typeFilter];
            const searchPromises = tablesToSearch.map(tableName => performSearchInTable(tableName, idTerm, nameTerm));
            const resultsByTable = await Promise.all(searchPromises);
            displayResults(resultsByTable.flat());
        } catch (error) {
            searchMessage.textContent = `Error: ${error.message}`;
        } finally {
            searchLoader.classList.add('hidden');
        }
    }

    async function performSearchInTable(tableName, idTerm, nameTerm) {
        const columns = searchableColumns[tableName];
        let query = supabaseClient.from(tableName).select('*');
        const filters = [];
        if (idTerm && columns.idFields.length > 0) { filters.push(`or(${columns.idFields.map(field => `"${field}".ilike.%${idTerm}%`).join(',')})`); }
        if (nameTerm && columns.nameFields.length > 0) { filters.push(`or(${columns.nameFields.map(field => `"${field}".ilike.%${nameTerm}%`).join(',')})`); }
        if (filters.length === 0) return [];
        query = query.or(filters.join(','));
        const { data, error } = await query;
        if (error) { console.error(`Error buscando en la tabla ${tableName}:`, error); return []; }
        return data.map(item => ({ ...item, sourceType: tableName }));
    }
    
    // ================================================================
    // ===== INICIO: FUNCIÓN displayResults MODIFICADA
    // ================================================================
    function displayResults(results) {
        if (results.length === 0) {
            searchMessage.textContent = 'No se encontraron resultados.'; return;
        }
        results.forEach(item => {
            const card = document.createElement('div');
            card.className = 'result-card';
            const displayName = tableDisplayNames[item.sourceType] || 'Desconocido';
            
            const primaryKeyName = tablePrimaryKeys[item.sourceType];
            const primaryKeyValue = item[primaryKeyName] || item.id;

            const title = document.createElement('h3');
            title.className = 'result-card-title';
            title.textContent = `Tipo: ${displayName} (ID: ${primaryKeyValue})`;
            card.appendChild(title);
            
            for (const key in item) {
                if (key !== 'sourceType' && key !== 'id' && item[key] !== null && String(item[key]).trim() !== '') {
                    const value = item[key];
                    const itemDiv = document.createElement('div');
                    itemDiv.className = 'result-item';
                    
                    const keySpan = document.createElement('span');
                    keySpan.className = 'result-key';
                    keySpan.textContent = key;

                    if (key === 'enlace') {
                        const placeholder = document.createElement('div'); // Espacio vacío para la primera columna
                        
                        const linkButton = document.createElement('a');
                        linkButton.href = value;
                        linkButton.textContent = 'Ver Enlace';
                        linkButton.target = '_blank';
                        linkButton.rel = 'noopener noreferrer';
                        linkButton.classList.add('link-button');

                        itemDiv.appendChild(placeholder);
                        itemDiv.appendChild(keySpan);
                        itemDiv.appendChild(linkButton);
                    } else {
                        const valueSpan = document.createElement('span');
                        valueSpan.className = 'result-value';
                        valueSpan.textContent = value;

                        const copyButton = document.createElement('button');
                        copyButton.className = 'copy-line-button';
                        copyButton.innerHTML = '&#128203;'; // Icono de portapapeles
                        copyButton.title = 'Copiar valor';
                        copyButton.dataset.text = value;

                        // Añadir los elementos en el nuevo orden: Botón, Etiqueta, Valor
                        itemDiv.appendChild(copyButton);
                        itemDiv.appendChild(keySpan);
                        itemDiv.appendChild(valueSpan);
                    }
                    card.appendChild(itemDiv);
                }
            }
            searchResultsContainer.appendChild(card);
        });
    }
    // ================================================================
    // ===== FIN: FUNCIÓN displayResults MODIFICADA
    // ================================================================

    function clearSearch() {
        searchForm.reset();
        searchResultsContainer.innerHTML = '';
        searchMessage.textContent = '';
    }

    function handleCopy(event) {
        if (!event.target.classList.contains('copy-line-button')) return;
        const button = event.target;
        const textToCopy = button.dataset.text;
        navigator.clipboard.writeText(textToCopy).then(() => {
            button.innerHTML = '✅';
            setTimeout(() => { button.innerHTML = '&#128203;'; }, 2000);
        }).catch(err => { alert('No se pudo copiar el texto.'); });
    }

    if (searchForm) {
        searchForm.addEventListener('submit', handleSearch);
        clearSearchButton.addEventListener('click', clearSearch);
        searchResultsContainer.addEventListener('click', handleCopy);
    }
});
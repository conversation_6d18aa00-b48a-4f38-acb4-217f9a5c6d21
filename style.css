/* Estilos generales */
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    background-color: #f4f7f9;
    color: #333;
    margin: 0;
}
.app-container { display: flex; min-height: 100vh; }
.hidden { display: none !important; }

/* --- Sidebar --- */
.sidebar { width: 250px; background-color: #2c3e50; color: #ecf0f1; padding: 20px; transition: margin-left 0.3s ease; flex-shrink: 0; box-shadow: 2px 0 5px rgba(0,0,0,0.1); }
body.sidebar-hidden .sidebar { margin-left: -290px; }
.sidebar-header h2 { margin: 0 0 20px 0; text-align: center; color: #fff; border-bottom: 1px solid #34495e; padding-bottom: 15px; }
.sidebar-nav .nav-title { font-size: 0.8rem; color: #95a5a6; text-transform: uppercase; margin: 15px 0 5px; }
.sidebar-nav hr { border: none; border-top: 1px solid #34495e; margin: 15px 0; }
.sidebar-nav button { width: 100%; padding: 12px 15px; font-size: 1rem; color: #ecf0f1; background-color: transparent; border: 1px solid #34495e; border-radius: 5px; cursor: pointer; text-align: left; transition: background-color 0.3s ease, color 0.3s ease; margin-top: 5px; }
.sidebar-nav button:hover { background-color: #3498db; color: #fff; }
.sidebar-nav button.active { background-color: #2980b9; font-weight: bold; color: #fff; }
.nav-main-button { background-color: #34495e !important; font-weight: bold; }

/* --- Contenido Principal --- */
.main-content { flex-grow: 1; padding: 25px; background-color: #fff; overflow-y: auto; }
.main-header { display: flex; align-items: center; gap: 15px; margin-bottom: 20px; border-bottom: 2px solid #e0e0e0; padding-bottom: 15px; }
#sidebar-toggle { background: #3498db; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 1.5rem; width: 40px; height: 40px; }
#sidebar-toggle:hover { background: #2980b9; }
.main-content h1 { margin: 0; }

/* --- Formularios (Búsqueda, Añadir) --- */
.search-form { background-color: #f8f9fa; padding: 20px; border-radius: 8px; border: 1px solid #dee2e6; display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; align-items: flex-end; }
.add-form { background-color: #f8f9fa; padding: 20px; border-radius: 8px; border: 1px solid #dee2e6; }
.table-selection-group { margin-bottom: 20px; }
.form-divider { border: none; border-top: 1px solid #dee2e6; margin: 0 0 20px 0; }
.form-group { display: flex; flex-direction: column; }
.form-group label { margin-bottom: 5px; font-weight: 500; color: #495057; }
.form-group input, .form-group select { padding: 10px; border: 1px solid #ced4da; border-radius: 4px; font-size: 1rem; }
.form-actions { display: flex; gap: 10px; align-items: flex-end; justify-content: flex-end; margin-top: 20px; }
.btn-primary { background-color: #007bff; color: white; padding: 10px 20px; font-size: 1rem; border: none; border-radius: 4px; cursor: pointer; }
.btn-primary:hover { background-color: #0056b3; }
.btn-primary:disabled { background-color: #a0c7f0; cursor: not-allowed; }
.btn-secondary { background-color: #6c757d; color: white; padding: 10px 20px; font-size: 1rem; border: none; border-radius: 4px; cursor: pointer; }
.btn-secondary:hover { background-color: #5a6268; }
#form-fields-container { display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin-bottom: 20px; }
@media (max-width: 1024px) { #form-fields-container { grid-template-columns: repeat(2, 1fr); } }
@media (max-width: 600px) { #form-fields-container { grid-template-columns: 1fr; } }


/* --- Resultados de búsqueda --- */
.search-results-container { 
    margin-top: 30px; 
}

.result-card {
    background: #fff;
    border: 1px solid #e9ecef;
    border-left: 5px solid #3498db;
    border-radius: 8px;
    padding: 20px 25px;
    margin-bottom: 25px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
}

.result-card-title {
    font-size: 1.5rem;
    color: #2c3e50;
    margin-top: 0;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

/* ================================================================ */
/* ===== INICIO: ESTILOS MODIFICADOS PARA DISEÑO DE 3 COLUMNAS === */
/* ================================================================ */

.result-item {
    display: grid;
    /* Define 3 columnas: [Botón], [Etiqueta], [Valor] */
    grid-template-columns: 40px 220px 1fr; 
    gap: 15px;
    align-items: center; /* Centra verticalmente los elementos en la fila */
    padding: 12px 0;
    border-bottom: 1px solid #f1f3f5;
}

.result-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.copy-line-button {
    background: transparent;
    border: none;
    cursor: pointer;
    font-size: 1.1rem;
    color: #adb5bd;
    padding: 5px;
    border-radius: 5px;
    transition: color 0.2s ease, background-color 0.2s ease;
    opacity: 1;
    justify-self: center; /* Centra el botón en su propia columna */
}

.copy-line-button:hover {
    color: #3498db;
    background-color: #eaf2f8;
}

.result-key {
    font-weight: 600;
    color: #495057;
    font-size: 0.95rem;
    word-break: break-word;
}

.result-value {
    color: #212529;
    font-size: 1rem;
    word-break: break-word;
}
/* ================================================================ */
/* ===== FIN: ESTILOS MODIFICADOS ================================= */
/* ================================================================ */

/* --- Visor de Tablas --- */
.loader { font-size: 1.2rem; color: #7f8c8d; text-align: center; padding: 40px; }
#table-container { width: 100%; overflow-x: auto; }
table { width: 100%; border-collapse: collapse; margin-top: 20px; }
th, td { padding: 10px 12px; border: 1px solid #ddd; text-align: left; white-space: nowrap; font-size: 0.9rem; }
thead th { background-color: #eaf2f8; position: sticky; top: 0; }
tbody tr:nth-child(even) { background-color: #f9f9f9; }
tbody tr:hover { background-color: #f1c40f; }
.link-button { padding: 4px 8px; font-size: 0.8rem; color: #fff; background-color: #27ae60; border-radius: 4px; text-decoration: none; display: inline-block; }

/* --- Estilos para botones de acción en tablas --- */
td:last-child { white-space: nowrap; }
.edit-btn, .delete-btn { padding: 5px 10px; font-size: 0.8rem; border: none; border-radius: 4px; color: white; cursor: pointer; margin-right: 5px; }
.edit-btn { background-color: #f39c12; }
.edit-btn:hover { background-color: #d35400; }
.delete-btn { background-color: #e74c3c; }
.delete-btn:hover { background-color: #c0392b; }

/* --- Estilos para el Modal de Edición --- */
.modal-overlay { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.6); display: flex; justify-content: center; align-items: center; z-index: 1000; }
.modal-content { background: #fff; padding: 25px; border-radius: 8px; width: 90%; max-width: 800px; max-height: 90vh; display: flex; flex-direction: column; }
.modal-header { display: flex; justify-content: space-between; align-items: center; border-bottom: 1px solid #ccc; padding-bottom: 15px; margin-bottom: 15px; }
.modal-header h2 { margin: 0; }
.close-button { background: none; border: none; font-size: 2rem; cursor: pointer; line-height: 1; }
.modal-body { overflow-y: auto; }
.form-fields-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; }
.modal-footer { margin-top: 20px; padding-top: 15px; border-top: 1px solid #ccc; display: flex; justify-content: flex-end; }
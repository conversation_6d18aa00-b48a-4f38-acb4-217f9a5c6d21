// script.js - Lógica para el visor de tablas, navegación y acciones CRUD

// --- CONFIGURACIÓN DE SUPABASE ---
const SUPABASE_URL = 'https://twxtlrjbpxifcdzepxfk.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InR3eHRscmpicHhpZmNkemVweGZrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTk0MzAwODgsImV4cCI6MjA3NTAwNjA4OH0.ycUkKeHQ7c2bPBA01EN1bi70kIEfS7D_Uc4K6FA1YNA';

const supabaseClient = supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// --- CABECERAS ---
const tableHeaders = {
    'Exp_Advos': ['enlace', 'N. GRAL', 'TIPO AUTORIZACIÓN', 'EXPEDIENTE', 'F. DE PRESENTACIÓN', 'PASAPORTE1', 'N.I.E1', '1er Apellido1', '2º Apellido1', 'Nombre1', 'Sexo1', 'F nac1', 'Lugar1', 'País1', 'Nacionalidad1', 'Estado civil1', 'Padre1', 'Madre1', 'Domicilio en España1', 'Nº1', 'Bl1', 'Piso1', 'Localidad1', 'C.P.1', 'Provincia1', 'Teléfono1', 'E-mail1', 'Rep. Legal2', 'DNI/NIE/PAS2', 'PASAPORTE2', 'D.N.I./NIE2', '1er Apellido2', '2º Apellido2', 'Nombre2', 'Sexo2', 'F nac2', 'Lugar2', 'País2', 'Nacionalidad2', 'Estado civil2', 'Padre2', 'Madre2', 'Domicilio en España2', 'Nº2', 'Piso2', 'Localidad2', 'C.P.2', 'Provincia2', 'Vínculo2', 'Autorizacion2', 'Escolariz2', 'NIF/CIF3', 'Nombre/Razón Socia3', 'Actividad3', 'Ocupación3', 'Domicilio3', 'Localidad3', 'C.P.3', 'Provincia3', 'Teléfono3', 'E-mail3', 'Representante3', 'DNI/NIE/PAS3', 'Título3', 'Domicilio en España3_Rep', 'Nº3_Rep', 'Piso3_Rep', 'Localidad3_Rep', 'C.P.3_Rep', 'Provincia3_Rep', 'Teléfono3_Rep', 'E-mail3_Rep', 'FECHA3', 'F. RESOLUCION3', 'F. NOTIFICACION3', 'F. RECURSO3', 'DECISION3', 'AUTORIZACION3'],
    'Nacionalidad': ['enlace', 'N.', 'NIE', 'Nº Soporte', 'NOMBRE', 'PRIMER APELLIDO', 'SEGUNDO APELLIDO', 'E CIVIL', 'SEXO', 'PAIS DE NACIMIENTO', 'LOCALIDAD DE NACIMIENTO', 'FECHA DE NACIMIENTO', 'FECHA RESIDENCIA', 'PADRE', 'MADRE', 'PROVINCIA', 'LOCALIDAD', 'TIPO DE VIA', 'NOMBRE DE VIA', 'NUMERO', 'PISO', 'LETRA', 'CODIGO POSTAL', 'TELEFONO', 'CORREO ELECTRONICO', 'NOMBRE2', 'APELLIDO2', '2º APELLIDO2', 'FECHA2', 'ESPOSO2', '2', '3', 'DNI/NIE2', 'NACIONALIDAD2', 'HIJO 1', 'APELLIDO HIJO 1', 'F NACIMIENTO HIJO 1', 'LUGAR HIJO 1', 'PAIS HIJO 1', 'HIJO 2', 'APELLLIDO HIJO 2', 'FECHA HIJO 2', 'LUGAR HIJO 2', 'PAIS HIJO 2', 'HIJO 3', 'APELLIDO HIJO 3', 'FECHA HIJO 3', 'LUGAR HIJO 3', 'PAIS HIJO 3'],
    'Proc_Judiciales': ['enlace', 'N. GRAL', 'NOMBRE', 'NIE/PASAPORTE', 'NACIONALIDAD', 'DOMICILIO', 'NÚMERO', 'MUNICIPIO', 'CÓDIGO POSTAL', 'PROVINCIA', 'TELEFONO', 'CORREO ELECTRONICO', 'EXPEDIENTE', 'OBJETO', 'F. SOLICITUD', 'FECHA', 'NOTIFICADA', 'MOTIVO 1', 'JUSTIFICACIÓN', 'FECHA DEMANDA', 'JUZGADO', 'NÚMERO DE PROCEDIMIENTO'],
    'Datos_Generales': ['enlace', 'NÚM GRAL', 'UBICACIÓN', 'PROCEDIMIENTO', 'NOMBRE', 'ESTADO', 'COMENTARIO']
};

const tablePrimaryKeys = {
    'Exp_Advos': 'N. GRAL',
    'Nacionalidad': 'N.',
    'Proc_Judiciales': 'N. GRAL',
    'Datos_Generales': 'NÚM GRAL'
};

document.addEventListener('DOMContentLoaded', () => {
    // --- ESTADO DE LA APLICACIÓN ---
    let currentActiveTable = null;

    // --- ELEMENTOS DEL DOM ---
    const sidebarToggleButton = document.getElementById('sidebar-toggle');
    const bodyElement = document.body;
    const searchPage = document.getElementById('search-page');
    const tableViewerPage = document.getElementById('table-viewer-page');
    const addRecordPage = document.getElementById('add-record-page');
    const navSearchButton = document.getElementById('nav-search-btn');
    const navAddButton = document.getElementById('nav-add-btn');
    const navTableButtons = document.querySelectorAll('.nav-table-btn');
    const tableNameH2 = document.getElementById('table-name');
    const tableContainer = document.getElementById('table-container');
    const loader = document.getElementById('loader');

    // --- ELEMENTOS DEL MODAL DE EDICIÓN ---
    const editModal = document.getElementById('edit-modal');
    const closeModalBtn = document.getElementById('close-modal-btn');
    const cancelEditBtn = document.getElementById('cancel-edit-btn');
    const editForm = document.getElementById('edit-form');
    const editFormFieldsContainer = document.getElementById('edit-form-fields');
    const saveChangesBtn = document.getElementById('save-changes-btn');

    // --- LÓGICA DE NAVEGACIÓN ---
    function showPage(pageToShow) {
        searchPage.classList.add('hidden');
        tableViewerPage.classList.add('hidden');
        addRecordPage.classList.add('hidden');
        pageToShow.classList.remove('hidden');
    }

    navSearchButton.addEventListener('click', () => { showPage(searchPage); currentActiveTable = null; });
    navAddButton.addEventListener('click', () => { showPage(addRecordPage); currentActiveTable = null; });

    // --- LÓGICA DEL VISOR DE TABLAS ---
    async function fetchAndDisplayData(tableName) {
        currentActiveTable = tableName;
        showPage(tableViewerPage);
        loader.classList.remove('hidden');
        tableContainer.innerHTML = '';
        tableNameH2.textContent = `Cargando tabla: ${tableName}...`;

        const { data, error } = await supabaseClient.from(tableName).select('*');
        loader.classList.add('hidden');

        if (error) {
            console.error('Error al obtener los datos:', error);
            tableNameH2.textContent = `Error al cargar la tabla ${tableName}`;
            return;
        }
        tableNameH2.textContent = `Mostrando tabla: ${tableName}`;
        renderTable(tableName, data);
    }

    function renderTable(tableName, data) {
        if (!data || data.length === 0) {
            tableContainer.innerHTML = '<p>La tabla está vacía.</p>';
            return;
        }
        const headers = tableHeaders[tableName];
        const primaryKeyColumn = tablePrimaryKeys[tableName];
        const table = document.createElement('table');
        const thead = document.createElement('thead');
        const tbody = document.createElement('tbody');
        const headerRow = document.createElement('tr');
        
        const thActions = document.createElement('th');
        thActions.textContent = 'Acciones';
        headerRow.appendChild(thActions);
        
        headers.forEach(headerText => {
            const th = document.createElement('th');
            th.textContent = headerText;
            headerRow.appendChild(th);
        });
        thead.appendChild(headerRow);

        data.forEach(rowData => {
            const row = document.createElement('tr');
            const tdActions = document.createElement('td');
            
            const editButton = document.createElement('button');
            editButton.textContent = 'Editar';
            editButton.className = 'edit-btn';
            editButton.dataset.id = rowData[primaryKeyColumn];
            editButton.dataset.table = tableName;

            const deleteButton = document.createElement('button');
            deleteButton.textContent = 'Eliminar';
            deleteButton.className = 'delete-btn';
            deleteButton.dataset.id = rowData[primaryKeyColumn];
            deleteButton.dataset.table = tableName;
            
            tdActions.appendChild(editButton);
            tdActions.appendChild(deleteButton);
            row.appendChild(tdActions);

            headers.forEach(header => {
                const td = document.createElement('td');
                if (header === 'enlace' && rowData[header]) {
                    const linkButton = document.createElement('a');
                    linkButton.href = rowData[header];
                    linkButton.textContent = 'Ver';
                    linkButton.target = '_blank';
                    linkButton.rel = 'noopener noreferrer';
                    linkButton.classList.add('link-button');
                    td.appendChild(linkButton);
                } else {
                    td.textContent = rowData[header] ?? '';
                }
                row.appendChild(td);
            });
            
            tbody.appendChild(row);
        });

        table.appendChild(thead);
        table.appendChild(tbody);
        tableContainer.innerHTML = '';
        tableContainer.appendChild(table);
    }

    // --- LÓGICA DE ACCIONES (EDITAR/ELIMINAR) ---
    async function handleDelete(id, tableName) {
        if (!id) {
            alert('Error: El identificador del registro es inválido.');
            return;
        }
        const confirmed = confirm('¿Estás seguro de que quieres eliminar este registro? Esta acción es irreversible.');
        if (!confirmed) return;

        const primaryKeyColumn = tablePrimaryKeys[tableName];
        // ================================================================
        // ===== INICIO: CORRECCIÓN - Usar filtro de texto para manejar nombres de columna con puntos
        // ================================================================
        const filterString = `"${primaryKeyColumn}".eq."${id}"`;
        const { error } = await supabaseClient.from(tableName).delete().or(filterString);
        // ================================================================
        // ===== FIN: CORRECCIÓN
        // ================================================================

        if (error) {
            alert(`Error al eliminar el registro: ${error.message}`);
        } else {
            alert('Registro eliminado con éxito.');
            fetchAndDisplayData(tableName);
        }
    }

    async function openEditModal(id, tableName) {
        if (!id) {
            alert('Error: El identificador del registro es inválido.');
            return;
        }
        
        const primaryKeyColumn = tablePrimaryKeys[tableName];
        // ================================================================
        // ===== INICIO: CORRECCIÓN - Usar filtro de texto para manejar nombres de columna con puntos
        // ================================================================
        const filterString = `"${primaryKeyColumn}".eq."${id}"`;
        const { data, error } = await supabaseClient.from(tableName).select('*').or(filterString).single();
        // ================================================================
        // ===== FIN: CORRECCIÓN
        // ================================================================

        if (error) {
            alert(`Error al obtener los datos para editar: ${error.message}`);
            return;
        }

        editFormFieldsContainer.innerHTML = '';
        const headers = tableHeaders[tableName];

        headers.forEach(header => {
            if (header === primaryKeyColumn) return; 
            
            const formGroup = document.createElement('div');
            formGroup.className = 'form-group';
            const label = document.createElement('label');
            label.textContent = header;
            const input = document.createElement('input');
            input.type = 'text';
            input.name = header;
            input.value = data[header] ?? '';
            
            formGroup.appendChild(label);
            formGroup.appendChild(input);
            editFormFieldsContainer.appendChild(formGroup);
        });

        editForm.dataset.id = id;
        editForm.dataset.table = tableName;
        editModal.classList.remove('hidden');
    }

    async function handleUpdate(event) {
        event.preventDefault();
        const id = editForm.dataset.id;
        const tableName = editForm.dataset.table;
        
        const formData = new FormData(editForm);
        const updatedData = {};
        for (const [key, value] of formData.entries()) {
            updatedData[key] = value.trim() === '' ? null : value.trim();
        }

        saveChangesBtn.textContent = 'Guardando...';
        saveChangesBtn.disabled = true;

        const primaryKeyColumn = tablePrimaryKeys[tableName];
        // ================================================================
        // ===== INICIO: CORRECCIÓN - Usar filtro de texto para manejar nombres de columna con puntos
        // ================================================================
        const filterString = `"${primaryKeyColumn}".eq."${id}"`;
        const { error } = await supabaseClient.from(tableName).update(updatedData).or(filterString);
        // ================================================================
        // ===== FIN: CORRECCIÓN
        // ================================================================

        saveChangesBtn.textContent = 'Guardar Cambios';
        saveChangesBtn.disabled = false;

        if (error) {
            alert(`Error al actualizar el registro: ${error.message}`);
        } else {
            alert('Registro actualizado con éxito.');
            editModal.classList.add('hidden');
            fetchAndDisplayData(tableName);
        }
    }

    // --- EVENT LISTENERS ---
    sidebarToggleButton.addEventListener('click', () => bodyElement.classList.toggle('sidebar-hidden'));

    navTableButtons.forEach(button => {
        button.addEventListener('click', () => {
            const tableName = button.dataset.table;
            fetchAndDisplayData(tableName);
        });
    });

    tableContainer.addEventListener('click', (event) => {
        const target = event.target;
        if (target.classList.contains('delete-btn')) {
            const { id, table } = target.dataset;
            handleDelete(id, table);
        } else if (target.classList.contains('edit-btn')) {
            const { id, table } = target.dataset;
            openEditModal(id, table);
        }
    });
    
    closeModalBtn.addEventListener('click', () => editModal.classList.add('hidden'));
    cancelEditBtn.addEventListener('click', () => editModal.classList.add('hidden'));
    editForm.addEventListener('submit', handleUpdate);

    // Estado inicial
    showPage(searchPage);
});